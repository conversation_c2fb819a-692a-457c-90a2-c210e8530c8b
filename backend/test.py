from retreival import get_today_date_tool,  search_tool, product_search_tool
# from prompts.react import prompt_react
from langchain import hub
from langchain.agents import AgentExecutor, create_react_agent,c
import os
from dotenv import load_dotenv
from openai import AsyncOpenAI
from langchain_openai import ChatOpenAI
from langchain.agents.types import StructuredChatAgent
load_dotenv(override=True)


GROQ_API_KEY=os.getenv("GROQ_API_KEY")

tools = [search_tool,product_search_tool, get_today_date_tool,]
retrieved_text = ""

# Load ReAct prompt
prompt_react = hub.pull("hwchase17/react")

# Initialize ChatGroq model for language understanding
model=ChatOpenAI(
    name="gpt-4o-mini",  # Use efficient model for query processing
    )
# Create ReAct agent
# react_agent = create_react_agent(model, tools=tools, prompt=prompt_react)
react_agent = StructuredChatAgent(model, allowed_tools=tools)
react_agent_executor = AgentExecutor(
    agent=react_agent, tools=tools, verbose=True, handle_parsing_errors=True
)


while True:
    user_input = input("User: ")
    if user_input.lower() in ["exit", "quit"]:
        print("Exiting the chat.")
        break

    # Execute the agent with the user input
    response = react_agent_executor.invoke({"input": user_input})
    
    # Print the response from the agent
    print(f"Agent: {response['output']}")
    
    # If the response contains retrieved text, print it
    if 'retrieved_text' in response:
        retrieved_text = response['retrieved_text']
        print(f"Retrieved Text: {retrieved_text}")