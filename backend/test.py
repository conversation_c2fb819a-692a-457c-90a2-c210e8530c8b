from retreival import get_today_date_tool, search_tool, product_search_tool
from langchain import hub
# from langchain.agents import AgentExecutor, create_react_agent
import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver  # an in-memory checkpointer
from langgraph.prebuilt import create_react_agent

load_dotenv(override=True)

# Verify OpenAI API key is loaded
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

tools = [search_tool, product_search_tool, get_today_date_tool]

# Load ReAct prompt
prompt_react = hub.pull("hwchase17/react")

# Initialize ChatOpenAI model with proper configuration
model = ChatOpenAI(
    model="gpt-4o-mini",  # Use model parameter instead of name
    temperature=0.1,
    api_key=OPENAI_API_KEY,
)

# Create ReAct agent (using the correct agent type for ReAct prompt)
react_agent = create_react_agent(model, tools=tools, prompt=prompt_react)
react_agent_executor = AgentExecutor(
    agent=react_agent,
    tools=tools,
    verbose=True,
    handle_parsing_errors=True,
    max_iterations=2,  # Reduce iterations to prevent loops
    return_intermediate_steps=False,
    max_execution_time=30  # Add timeout to prevent hanging
)

config = {"configurable": {"thread_id": "test-thread"}}
print("Chat application started. Type 'exit' or 'quit' to stop.")
print("Available commands:")
print("- Ask questions about products (will use Product Search)")
print("- Ask technical questions (will use QNA Search)")
print("- Ask for today's date")

while True:
    try:
        user_input = input("\nUser: ")
        if user_input.lower() in ["exit", "quit"]:
            print("Exiting the chat.")
            break

        # Execute the agent with the user input
        print("Processing your request...")
        response = langgraph_agent_executor.invoke({"input": user_input}, config=config)

        # Print the response from the agent
        print(f"\nAgent: {response["messages"][-1].content}")

    except KeyboardInterrupt:
        print("\nExiting the chat.")
        break
    except Exception as e:
        print(f"\nError occurred: {str(e)}")
        print("Please try again or type 'exit' to quit.")