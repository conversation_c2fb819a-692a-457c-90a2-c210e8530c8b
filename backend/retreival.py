from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI
from llama_index.core import VectorStoreIndex
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.query_engine import Retriever<PERSON><PERSON>yEng<PERSON>
from qdrant_client import AsyncQdrantClient, QdrantClient
from llama_index.core import Settings
from dotenv import load_dotenv
import os
import asyncio
from typing import Literal, Union
from pydantic import BaseModel
from openai import AsyncOpenAI
from langchain.tools import tool
from langchain.agents import Tool

load_dotenv()
sync_client = QdrantClient(
    host="*************",
    port=6333,
    api_key=None,
    timeout=10,
    https=False,
    prefer_grpc=False

)
async_client = AsyncQdrantClient(
    host="*************",
    port=6333,
    api_key=None,
    timeout=10,
    https=False,
    prefer_grpc=False

)
search_vector_store = QdrantVectorStore(
                client=sync_client,  # Sync client for sync operations
                aclient=async_client,  # Async client for async operations
                parallel=4,
                collection_name="ag_salessupport_sentence_context",
                enable_hybrid=False

            )



product_vector_store = QdrantVectorStore(
                client=sync_client,  # Sync client for sync operations
                aclient=async_client,  # Async client for async operations
                parallel=4,
                collection_name="ag_products",
                enable_hybrid=False
            )


search_vector_store_index = VectorStoreIndex.from_vector_store(
    vector_store=search_vector_store)

product_vector_store_index = VectorStoreIndex.from_vector_store(
    vector_store=product_vector_store)


search_retriever = VectorIndexRetriever(
    index=search_vector_store_index,
    similarity_top_k=10)
product_retriever = VectorIndexRetriever(
    index=product_vector_store_index)
# Settings.llm = OpenAI(
#                 model="gpt-4o-mini",  # Use efficient model for query processing
#                 temperature=0.1,
#                 api_key=os.getenv("OPENAI_API_KEY"),
#             )



def qna_search(query: str):
    """Search for relevant documents based on the query."""
    results =search_retriever.retrieve(query)
    response = f"Here are some relevant documents for your query: "
    if not results:
        response += "No relevant documents found."
    else:
        response += "Found the following documents: "
        for doc in results:
            # Assuming each doc has a 'node' attribute with the content
            # and a 'score' attribute for relevance
            node = doc.node.to_dict()
            response += f" Score {doc.score} {node.get('text', 'Unknown Document')} (Metadata: {node.get('metadata', {})})\n"
    return results

def product_search(query: str):
    """Process the query using the LLM and return the response."""
    result = product_retriever.retrieve(query)
    # print(f"Product query results for query '{query}':")
    response= f"Here are some products that match your query: "
    if not result:
        response += "No relevant products found."
    else:
        response += "Found the following products: "
        for doc in result:  
            # Assuming each doc has a 'node' attribute with the content
            # and a 'score' attribute for relevance
            node=doc.node.to_dict()
            response += f" Score {doc.score} {node.get('text', "Unknown Product"),} (Metadata: {node.get('metadata', {})})\n"
    return result




def get_today_date(input : str) -> str:
    import datetime
    today = datetime.date.today()
    return f"\n {today} \n"


get_today_date_tool = Tool(
    name="Ottieni data",
    func=get_today_date,
    description="Useful for getting today's date"
)

search_tool = Tool(
    name="Search",
    func=qna_search,
    description="Useful for searching relevant documents based on a query"
)
product_search_tool = Tool(
    name="Product Search",
    func=product_search,
    description="Useful for searching products based on a query"
)